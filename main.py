
import json
import io
import pandas as pd
import logging
import time

import sqlalchemy
import os
import requests
from datetime import datetime
from google.cloud.sql.connector import Connector
import functions_framework

from sqlalchemy import text
from whatsapp_helper import send_whatsapp_message, upload_media_to_whatsapp, batch_upload_media, send_messages_batch, send_conciliation_message
from utils import format_phone_number, prepare_conciliation_parameters
from db_manager import create_message_entries_batch, update_campaign_status,update_master_excel_status, get_dispute_ids_by_loan_ids
from storage_manager import read_file_from_gcs, GCS_BUCKET_NAME

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Enhanced batch processing configuration with adaptive sizing
WHATSAPP_MESSAGE_DELAY = float(os.environ.get('WHATSAPP_MESSAGE_DELAY', 0.8))  # Slightly increased
CALLBACK_URL = os.environ.get('CALLBACK_URL', 'https://your-django-app.com/api/whatsapp/batch/callback')

# Adaptive batch sizes based on message volume
def get_optimal_sub_batch_size(total_messages):
    """Calculate optimal sub-batch size based on total message volume"""
    if total_messages <= 100:
        return 8  # Small batches for small volumes
    elif total_messages <= 500:
        return 10  # Medium batches
    elif total_messages <= 2000:
        return 12  # Larger batches for medium volumes
    elif total_messages <= 10000:
        return 8   # Back to smaller batches for large volumes to manage rate limits
    else:
        return 6   # Very conservative for huge volumes (20k+)

def get_optimal_media_batch_size(total_files):
    """Calculate optimal media batch size based on file volume"""
    if total_files <= 50:
        return 4
    elif total_files <= 200:
        return 5
    elif total_files <= 1000:
        return 4
    else:
        return 3  # Very conservative for large file volumes

pool = None

def connect_to_instance() -> sqlalchemy.engine.base.Engine:    
    connector = Connector(refresh_strategy="lazy")
    instance_conn_name = os.environ['INSTANCE_CONNECTION_NAME']
    db_name = os.environ.get('DB_NAME')
    db_user = os.environ.get('DB_USER')
    db_password = os.environ.get('DB_PASSWORD')
    
    def getconn():
        return connector.connect(
            instance_conn_name,
            'pg8000',
            user=db_user,
            password=db_password,
            db=db_name
        )

    return sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        pool_size=25,  # Reduced pool size for stability
        max_overflow=5,
        pool_timeout=45,  # Increased timeout
        pool_recycle=300
    )

# def get_dispute_id_by_loan_id(pool, loan_id, campaign_id=None):
#     """
#     Get dispute_id for a given loan_id.
    
#     Args:
#         pool: Database connection pool
#         loan_id: The loan ID to search for
#         campaign_id: Optional campaign ID for additional filtering
        
#     Returns:
#         dispute_id if found, None otherwise
#     """
#     try:
#         # First try to get dispute_id using loan_id
#         query = text("""
#             SELECT id FROM arbitration_dispute 
#             WHERE loan_id = :loan_id
#             ORDER BY created_at DESC
#             LIMIT 1
#         """)
        
#         with pool.connect() as connection:
#             result = connection.execute(query, {"loan_id": str(loan_id).strip()})
#             row = result.fetchone()
            
#             if row:
#                 dispute_id = row[0]
#                 logger.debug(f"Found dispute_id {dispute_id} for loan_id {loan_id}")
#                 return dispute_id
            
#             # If not found by loan_id and we have campaign_id, try using campaign relationship
#             if campaign_id:
#                 campaign_query = text("""
#                     SELECT id FROM arbitration_dispute 
#                     WHERE campaign_id = :campaign_id
#                     ORDER BY created_at DESC
#                     LIMIT 1
#                 """)
                
#                 result = connection.execute(campaign_query, {"campaign_id": campaign_id})
#                 row = result.fetchone()
                
#                 if row:
#                     dispute_id = row[0]
#                     logger.debug(f"Found dispute_id {dispute_id} for campaign_id {campaign_id}")
#                     return dispute_id
            
#             # If still not found, log and return None
#             logger.warning(f"No dispute found for loan_id {loan_id}" + 
#                          (f" or campaign_id {campaign_id}" if campaign_id else ""))
#             return None
            
#     except Exception as e:
#         logger.error(f"Error fetching dispute_id for loan_id {loan_id}: {str(e)}")
#         return None

# def get_dispute_ids_batch(pool, loan_ids, campaign_id=None):
#     """
#     Get dispute_ids for multiple loan_ids in a single query for better performance.
    
#     Args:
#         pool: Database connection pool
#         loan_ids: List of loan IDs to search for
#         campaign_id: Optional campaign ID for fallback
        
#     Returns:
#         Dictionary mapping loan_id to dispute_id
#     """
#     try:
#         if not loan_ids:
#             return {}
        
#         # Clean and prepare loan_ids
#         clean_loan_ids = [str(loan_id).strip() for loan_id in loan_ids if loan_id]
        
#         if not clean_loan_ids:
#             return {}
        
#         # Create a query with IN clause for batch fetching
#         placeholders = ','.join([f':loan_id_{i}' for i in range(len(clean_loan_ids))])
        
#         query = text(f"""
#             SELECT loan_id, id FROM arbitration_dispute 
#             WHERE loan_id IN ({placeholders})
#             ORDER BY loan_id, created_at DESC
#         """)
        
#         # Create parameters dictionary
#         params = {f'loan_id_{i}': loan_id for i, loan_id in enumerate(clean_loan_ids)}
        
#         dispute_map = {}
        
#         with pool.connect() as connection:
#             result = connection.execute(query, params)
            
#             # Process results - only take the first (most recent) dispute per loan_id
#             seen_loan_ids = set()
#             for row in result:
#                 loan_id, dispute_id = row
#                 if loan_id not in seen_loan_ids:
#                     dispute_map[loan_id] = dispute_id
#                     seen_loan_ids.add(loan_id)
            
#             logger.info(f"Found disputes for {len(dispute_map)} out of {len(clean_loan_ids)} loan IDs")
            
#             # For loan_ids without disputes, try campaign-based fallback if available
#             missing_loan_ids = set(clean_loan_ids) - seen_loan_ids
#             if missing_loan_ids and campaign_id:
#                 logger.info(f"Attempting campaign-based fallback for {len(missing_loan_ids)} missing loan IDs")
                
#                 # For missing loan_ids, we could create new disputes or use a default dispute_id
#                 # This depends on your business logic
                
#         return dispute_map
        
#     except Exception as e:
#         logger.error(f"Error in batch dispute_id fetch: {str(e)}")
#         return {}

def read_excel_sheets_from_gcs(bucket_name, excel_file_path, flow_type):
    """
    Read Excel file from GCS and return DataFrames for different sheets based on flow type.
    """
    try:
        # Read the Excel file from GCS
        excel_data = read_file_from_gcs(bucket_name, excel_file_path)
        excel_buffer = io.BytesIO(excel_data)
        
        if flow_type == 'conciliation':
            # For conciliation, read multiple sheets
            # logger.info("Reading Excel file for conciliation flow - processing multiple sheets")
            
            # Read all sheets
            all_sheets = pd.read_excel(excel_buffer, sheet_name=None)  # Returns dict of all sheets
            
            # Log available sheets
            sheet_names = list(all_sheets.keys())
            # logger.info(f"Available sheets: {sheet_names}")
            
            # You can define which sheets to process based on your business logic
            # For example, if you have specific sheet names:
            primary_sheet_name = sheet_names[0] if sheet_names else None  # First sheet
            additional_sheets = {}
            
            if len(sheet_names) > 1:
                # Process additional sheets (you can customize this logic)
                for i, sheet_name in enumerate(sheet_names[1:], 1):
                    additional_sheets[f'sheet_{i}'] = all_sheets[sheet_name]
                
                # Single summary log instead of individual sheet logs
                # logger.info(f"Loaded {len(sheet_names) - 1} additional sheets with total {sum(len(sheet) for sheet in additional_sheets.values())} rows")
            
            return {
                'primary_data': all_sheets[primary_sheet_name] if primary_sheet_name else pd.DataFrame(),
                'additional_sheets': additional_sheets,
                'all_sheets': all_sheets
            }
        else:
            # For other flow types, read only the first sheet
            # logger.info("Reading Excel file for standard flow - processing first sheet only")
            df = pd.read_excel(excel_buffer, sheet_name=0)
            return {
                'primary_data': df,
                'additional_sheets': {},
                'all_sheets': {0: df}
            }
            
    except Exception as e:
        logger.error(f"Error reading Excel file from GCS: {str(e)}")
        raise

def calculate_dynamic_batch_size(current_success_rate, total_messages, current_hour_usage):
    """Calculate optimal batch size based on success rate, volume, and hourly usage"""
    base_size = get_optimal_sub_batch_size(total_messages)
    
    # Reduce batch size if approaching hourly limits
    if current_hour_usage > 3200:  # 80% of our 4000 conservative limit
        base_size = max(base_size // 3, 4)
        logger.warning(f"Near hourly limit, reducing batch size to {base_size}")
    elif current_hour_usage > 2800:  # 70% of limit
        base_size = max(base_size // 2, 5)
        logger.info(f"Approaching hourly limit, reducing batch size to {base_size}")
    
    # Adjust based on success rate
    if current_success_rate > 0.95:  # Excellent success
        return min(base_size + 2, 15)  # Can be slightly more aggressive
    elif current_success_rate > 0.85:  # Good success
        return base_size
    elif current_success_rate > 0.7:   # Moderate success
        return max(base_size - 2, 5)
    else:  # Poor success
        return max(base_size // 2, 4)  # Very conservative


@functions_framework.cloud_event
def process_batch(cloud_event):
    """Process a batch of WhatsApp messages triggered by a cloud event with enhanced error handling."""
    start_time = datetime.now()
    batch_errors = []  # Track errors during processing
    campaign_id = None  # Initialize early to use in finally block

    try:
        # Extract data from the cloud event
        if cloud_event.data:
            event_data = cloud_event.data
            
            # If the data is base64 encoded (common with Pub/Sub), decode it
            if isinstance(event_data, dict) and 'message' in event_data and 'data' in event_data['message']:
                import base64
                encoded_data = event_data['message']['data']
                decoded_data = base64.b64decode(encoded_data).decode('utf-8')
                try:
                    request_data = json.loads(decoded_data)
                except json.JSONDecodeError:
                    logger.error("Failed to parse cloud event data as JSON")
                    return json.dumps({'error': 'Invalid JSON in cloud event data'})
            else:
                # Handle other cloud event data formats if needed
                request_data = event_data
        else:
            logger.error("No data in cloud event")
            return json.dumps({'error': 'No data in cloud event'})

        # Extract parameters
        campaign_id = request_data.get('campaign_id')
        root_template_id = request_data.get('root_template_id')
        template_name = request_data.get('template_name')
        template_id = request_data.get('template_id')
        whatsapp_template_id = request_data.get('whatsapp_template_id')
        lang_code = request_data.get('lang_code', 'en')
        attachment_required = request_data.get('attachment_required')
        excel_file_path = request_data.get('excel_file_path')
        pdf_file_path = request_data.get('pdf_file_path')
        user_id = request_data.get('user_id')
        batch_number = request_data.get('batch_number', 0)
        total_batches = request_data.get('total_batches', 1)
        is_s21_notice = request_data.get('is_s21_notice')
        df_json = request_data.get('df_json')
        flow_type = request_data.get('flow_type', 'arbitration')  # Default to arbitration
        client_name = request_data.get('client_name')
        has_body_params = request_data.get('has_body_params')
        has_co_borrowers = request_data.get('has_co_borrowers', False)
        template_mapping = request_data.get('template_mapping', {})
        file_upload = request_data.get('file_upload', False)
        folder_name = request_data.get('folder_name', False)
        bucket_name = GCS_BUCKET_NAME

        # Conciliation-specific parameters
        is_termination_notice = request_data.get('is_termination_notice', False)
        is_s138_notice = request_data.get('is_s138_notice', False)
        is_payment_request_notice = request_data.get('is_payment_request_notice', False)
        sarfaeri_act_notice = request_data.get('sarfaeri_act_notice', False)

        conciliation_notice_1 = request_data.get('conciliation_notice_1', False)
        conciliation_notice_2 = request_data.get('conciliation_notice_2', False)
        conciliation_notice_3 = request_data.get('conciliation_notice_3', False)
        conciliation_notice_4 = request_data.get('conciliation_notice_4', False)
        template_parameter_mapping = request_data.get('template_parameter_mapping', {})
        

        # NEW: Extract Sheet 2 data for conciliation flow
        sheet2_data = request_data.get('sheet2_data', {})
        # logger.info(f"Sheet2 data received: {sheet2_data}")

        logger.info(f"Processing {flow_type} batch {batch_number + 1}/{total_batches} for campaign {campaign_id}")

        # if has_co_borrowers:
            # logger.info("Processing co-borrower data in this batch")

        # Connect to database
        global pool
        if not pool:
            pool = connect_to_instance()

        # Get the current state (messages sent count and processed_rows) if this is not the first batch
        current_messages_sent = 0
        current_processed_rows = 0
        try:
            # Query to get the current state
            query = text("""
                SELECT whatsapp_messages_sent, whatsapp_processed_rows 
                FROM notice_whatsapptemplate 
                WHERE id = :template_id
            """)
            
            with pool.connect() as connection:
                result = connection.execute(query, {"template_id": template_id})
                row = result.fetchone()
                if row:
                    current_messages_sent = row[0] if row[0] is not None else 0
                    current_processed_rows = row[1] if row[1] is not None else 0
                    # logger.info(f"Current state: messages_sent={current_messages_sent}, processed_rows={current_processed_rows}")
        except Exception as e:
            logger.error(f"Error getting current state: {str(e)}")
            # Continue with defaults
        
        # Ensure processed_rows only increases sequentially
        batch_start_processed = current_processed_rows

        # Update campaign status to 'sending' if this is the first batch
        if batch_number == 0:
            update_success = update_campaign_status(pool, campaign_id, template_id, 'processing', 0, 0, [])
            # logger.info(f"Initial status update {'successful' if update_success else 'failed'}")
        
        # Parse batch data
        if df_json:
            df = pd.read_json(df_json, orient='records')
            # logger.info(f"Batch {batch_number}: Processing {len(df)} records")
        else:
            error_msg = "No batch data provided"
            logger.error(error_msg)
            batch_errors.append(error_msg)
            update_campaign_status(pool, campaign_id, template_id, 'failed', current_processed_rows, current_messages_sent, batch_errors)
            return json.dumps({'error': error_msg})

        # Get dispute_ids for all loan_ids in this batch
        loan_ids = df['Loan ID'].tolist()
        loan_to_dispute_map = get_dispute_ids_by_loan_ids(pool, loan_ids, campaign_id)
        logger.info(f"Retrieved dispute mapping for {len(loan_to_dispute_map)} loan IDs")

        # Check for co-borrower columns if has_co_borrowers is True
        co_borrower_columns = []
        if has_co_borrowers:
            # Check which co-borrower number columns actually exist in the dataframe
            for i in range(1, 6):  # Up to 4 co-borrowers
                number_col = f"Co-borrower's Number{i}"
                if number_col in df.columns:
                    co_borrower_columns.append(number_col)

            # logger.info(f"Found {len(co_borrower_columns)} co-borrower number columns in the data")

        # Calculate optimal sub-batch size with enhanced logic
        total_records = len(df)
        initial_success_rate = 1.0  # Start optimistic
        optimal_batch_size = calculate_dynamic_batch_size(
            initial_success_rate, 
            total_records, 
            current_messages_sent % 4000
        )
        
        num_sub_batches = (total_records + optimal_batch_size - 1) // optimal_batch_size  # Ceiling division
        
        # logger.info(f"Using optimal sub-batch size: {optimal_batch_size} for {total_records} records")
        # logger.info(f"Dividing batch into {num_sub_batches} sub-batches")
        
        # Track overall results with enhanced metrics
        results = []
        successful_count = 0  # Count of messages sent in this batch
        failed_count = 0
        message_entries = []
        
        # Process each sub-batch separately with enhanced rate limiting
        for sub_batch_idx in range(num_sub_batches):
            sub_batch_start = sub_batch_idx * optimal_batch_size
            sub_batch_end = min(sub_batch_start + optimal_batch_size, total_records)
            sub_df = df.iloc[sub_batch_start:sub_batch_end]
            
            # logger.info(f"Processing sub-batch {sub_batch_idx + 1}/{num_sub_batches} with {len(sub_df)} records")
            sub_batch_start_time = time.time()
            
            # Calculate the sequential processed rows for this sub-batch
            processed_rows_for_update = batch_start_processed + sub_batch_end
            
            # 1. ENHANCED MEDIA UPLOAD PHASE
            media_cache = {}
            if attachment_required:
                # Prepare media data for this sub-batch
                file_data_map = {}
                for _, row in sub_df.iterrows():
                    loan_id = str(row['Loan ID']).strip()
                    
                    # Conditional PDF path based on file_upload parameter
                    if file_upload and folder_name:
                        base_path = f"notices/{campaign_id}/files/{folder_name}/{loan_id}"
                    elif file_upload:
                        base_path = f"notices/{campaign_id}/files/{loan_id}"
                    else:
                        base_path = f"notices/{campaign_id}/{root_template_id}/{template_name}/{loan_id}"

                    # Try both PDF extensions
                    pdf_data = None
                    successful_path = None
                    extensions = ['.pdf', '.PDF']

                    for ext in extensions:
                        try:
                            loan_pdf_path = f"{base_path}{ext}"
                            # logger.debug(f"Trying path: {loan_pdf_path}")
                            pdf_data = read_file_from_gcs(bucket_name, loan_pdf_path)
                            successful_path = loan_pdf_path
                            break  # Success! Exit the loop
                        except Exception as e:
                            # logger.debug(f"Failed to read {loan_pdf_path}: {str(e)}")
                            continue

                    # Handle the result
                    if pdf_data is not None:
                        file_data_map[loan_id] = pdf_data
                        # logger.debug(f"Successfully loaded PDF from: {successful_path}")
                    else:
                        error_msg = f"Error reading PDF for loan {loan_id}. Tried both .pdf and .PDF extensions from base path {base_path}"
                        logger.warning(error_msg)
                        batch_errors.append(error_msg)

                # Batch upload all media for this sub-batch with adaptive batch size
                if file_data_map:
                    media_batch_size = get_optimal_media_batch_size(len(file_data_map))
                    # logger.info(f"Sub-batch {sub_batch_idx + 1}: Uploading {len(file_data_map)} media files with batch size {media_batch_size}")
                    
                    media_ids = batch_upload_media(
                        {f"{loan_id}.pdf": data for loan_id, data in file_data_map.items()}, 
                        is_s21_notice,
                        batch_size=media_batch_size
                    )

                    # Convert results to our media_cache format
                    for loan_id in file_data_map.keys():
                        file_name = f"{loan_id}.pdf"
                        if file_name in media_ids:
                            media_cache[loan_id] = {
                                'media_id': media_ids[file_name],
                                'file_name': file_name
                            }

                    # logger.info(f"Sub-batch {sub_batch_idx + 1}: Successfully uploaded {len(media_cache)} out of {len(file_data_map)} media files")

                    # Track errors for missing media
                    missing_media_count = len(file_data_map) - len(media_cache)
                    if missing_media_count > 0:
                        error_msg = f"Failed to upload {missing_media_count} media files in sub-batch {sub_batch_idx + 1}"
                        logger.warning(error_msg)
                        batch_errors.append(error_msg)

            # 2. ENHANCED MESSAGE SENDING PHASE
            sub_messages_batch = []
            sub_results = []
            
            # Build the messages batch for this sub-batch (including co-borrowers)
            for _, row in sub_df.iterrows():
                try:
                    loan_id = str(row['Loan ID']).strip()

                    # Get dispute_id for this loan_id
                    dispute_id = loan_to_dispute_map.get(loan_id)
                    if not dispute_id:
                        logger.warning(f"No dispute_id found for loan_id {loan_id}")
                        dispute_id = None  # or you could skip this loan entirely

                    # Create a list to store all message tasks (primary borrower + co-borrowers)
                    all_message_tasks = []

                    # Add primary borrower message
                    raw_phone_number = str(row['Borrower\'s Number']).strip()
                    phone_number = format_phone_number(raw_phone_number)

                    if phone_number:
                        all_message_tasks.append({
                            'phone_number': phone_number,
                            'raw_phone_number': raw_phone_number,
                            'loan_id': loan_id,
                            'borrower_type': 'primary',
                            'borrower_name': row.get('Name of the Borrower', '').strip() if 'Name of the Borrower' in row else '',
                            'dispute_id': dispute_id
                        })
                    else:
                        error_msg = f"Invalid primary phone number: {raw_phone_number} for loan {loan_id}"
                        logger.warning(error_msg)
                        batch_errors.append(error_msg)

                    # Add co-borrower messages if available
                    if has_co_borrowers:
                        for i in range(1, 6):  # Up to 4 co-borrowers
                            number_col = f"Co-borrower's Number{i}"
                            name_col = f"Name of the Co-borrower{i}"
                            
                            if number_col in row and pd.notna(row[number_col]):
                                co_raw_phone = str(row[number_col]).strip()
                                co_phone = format_phone_number(co_raw_phone)
                                co_name = str(row[name_col]).strip() if name_col in row and pd.notna(row[name_col]) else ''
                                
                                if co_phone:
                                    all_message_tasks.append({
                                        'phone_number': co_phone,
                                        'raw_phone_number': co_raw_phone,
                                        'loan_id': loan_id,
                                        'borrower_type': f'co-borrower-{i}',
                                        'borrower_name': co_name,
                                        'dispute_id': dispute_id
                                    })
                                else:
                                    error_msg = f"Invalid co-borrower phone number: {co_raw_phone} for loan {loan_id}"
                                    logger.warning(error_msg)
                                    batch_errors.append(error_msg)

                    # Process all message tasks for this loan
                    for task in all_message_tasks:
                        try:
                            phone_number = task['phone_number']
                            raw_phone_number = task['raw_phone_number']
                            borrower_type = task['borrower_type']
                            borrower_name = task['borrower_name']
                            dispute_id = task['dispute_id']

                            # Handle attachment
                            media_id = None
                            file_name = None

                            if attachment_required and loan_id in media_cache:
                                media_id = media_cache[loan_id]['media_id']
                                file_name = media_cache[loan_id]['file_name']
                            elif attachment_required:
                                # Skip messages where media upload failed
                                error_msg = f"Media not available for loan {loan_id}, skipping message to {borrower_type}"
                                logger.warning(error_msg)
                                batch_errors.append(error_msg)
                                failed_count += 1
                                sub_results.append({
                                    'phone_number': phone_number,
                                    'loan_id': loan_id,
                                    'status': 'failed',
                                    'reason': 'Media upload failed',
                                    'borrower_type': borrower_type
                                })
                                continue

                            # Prepare message data based on flow type
                            if flow_type == 'conciliation':
                                # Prepare parameters based on template type
                                body_parameters = prepare_conciliation_parameters(
                                    row,
                                    client_name,
                                    is_termination_notice,
                                    is_s138_notice,
                                    is_payment_request_notice,
                                    sarfaeri_act_notice,
                                    conciliation_notice_1,
                                    conciliation_notice_2,
                                    conciliation_notice_3,
                                    conciliation_notice_4,
                                    template_parameter_mapping,
                                    sheet2_data  # Pass Sheet 2 data
                                )
                                
                                # Add to sub-batch messages for conciliation
                                sub_messages_batch.append({
                                    'mobile': phone_number,
                                    'template_id': whatsapp_template_id,
                                    'parameters': body_parameters,  # Use parameters instead of body_param_text
                                    'media_id': media_id,
                                    'file_name': file_name,
                                    'lang_code': lang_code,
                                    'loan_id': loan_id,
                                    'raw_phone_number': raw_phone_number,
                                    'borrower_type': borrower_type,
                                    'borrower_name': borrower_name,
                                    'flow_type': 'conciliation',
                                    'dispute_id': dispute_id
                                })
                            else:
                                # Original arbitration flow
                                sub_messages_batch.append({
                                    'mobile': phone_number,
                                    'template_id': whatsapp_template_id,
                                    'body_param_text': client_name if has_body_params is True else None,
                                    'media_id': media_id,
                                    'file_name': file_name,
                                    'lang_code': lang_code,
                                    'loan_id': loan_id,
                                    'raw_phone_number': raw_phone_number,
                                    'borrower_type': borrower_type,
                                    'borrower_name': borrower_name,
                                    'flow_type': 'arbitration',
                                    'dispute_id': dispute_id
                                })

                        except Exception as e:
                            error_msg = f"Error preparing message for {borrower_type} of loan {loan_id}: {str(e)}"
                            logger.error(error_msg)
                            batch_errors.append(error_msg)
                            failed_count += 1
                            sub_results.append({
                                'phone_number': task.get('raw_phone_number', 'unknown'),
                                'loan_id': loan_id,
                                'status': 'failed',
                                'reason': str(e),
                                'borrower_type': task.get('borrower_type', 'unknown')
                            })

                except Exception as e:
                    error_msg = f"Error processing loan {loan_id if 'loan_id' in locals() else 'unknown'}: {str(e)}"
                    logger.error(error_msg)
                    batch_errors.append(error_msg)
                    failed_count += 1
                    sub_results.append({
                        'phone_number': 'unknown',
                        'loan_id': loan_id if 'loan_id' in locals() else 'unknown',
                        'status': 'failed',
                        'reason': str(e),
                        'borrower_type': 'unknown'
                    })

            # Send all messages for this sub-batch with enhanced rate limiting
            if sub_messages_batch:
                # logger.info(f"Sub-batch {sub_batch_idx + 1}: Sending {len(sub_messages_batch)} messages")
                
                # Calculate current success rate for adaptive batching
                current_sub_batch_success_rate = 1.0  # Start optimistic
                if sub_batch_idx > 0:
                    # Calculate success rate from previous sub-batches
                    total_attempts = sum(len(results[i:i+optimal_batch_size]) for i in range(0, len(results), optimal_batch_size) if i < sub_batch_idx * optimal_batch_size)
                    if total_attempts > 0:
                        current_sub_batch_success_rate = successful_count / total_attempts
                
                # Calculate dynamic message batch size
                message_batch_size = min(
                    get_optimal_sub_batch_size(len(sub_messages_batch)),
                    len(sub_messages_batch)
                )
                
                # Adjust delay based on current performance and load
                current_hour_usage = (current_messages_sent + successful_count) % 4000
                dynamic_delay = WHATSAPP_MESSAGE_DELAY
                
                # Increase delay if approaching hourly limits or poor performance
                if current_hour_usage > 3200:
                    dynamic_delay = 3.0
                elif current_hour_usage > 2800:
                    dynamic_delay = 2.0
                elif current_sub_batch_success_rate < 0.8:
                    dynamic_delay = 2.5
                elif current_sub_batch_success_rate < 0.9:
                    dynamic_delay = 1.5
                
                # logger.info(f"Using message batch size: {message_batch_size}, delay: {dynamic_delay:.1f}s")
                
                batch_results = send_messages_batch(
                    sub_messages_batch, 
                    is_s21_notice, 
                    has_body_params, 
                    delay=dynamic_delay,
                    batch_size=message_batch_size
                )
                
                # Process results with enhanced error tracking
                for result in batch_results:
                    # print("******** result ********", result)
                    msg_data = result.get('original_data', {})
                    loan_id = msg_data.get('loan_id', 'unknown')
                    phone_number = msg_data.get('mobile', 'unknown')
                    raw_phone_number = msg_data.get('raw_phone_number', 'unknown')
                    borrower_type = msg_data.get('borrower_type', 'primary')
                    borrower_name = msg_data.get('borrower_name', '')
                    dispute_id = msg_data.get('dispute_id', '')

                    message_entries.append({
                        'loan_id': loan_id,
                        'msg_id': result.get('msg_id'),
                        'phone_number': phone_number,
                        'status': 'initiated',
                        'timestamp': datetime.now(),
                        'user_id': user_id,
                        'borrower_type': borrower_type,
                        'dispute_id': dispute_id,
                        'template_id': root_template_id
                    })
                    
                    if result['status'] == 'success':
                        successful_count += 1
                        sub_results.append({
                            'phone_number': phone_number,
                            'loan_id': loan_id,
                            'status': 'success',
                            'msg_id': result['msg_id'],
                            'borrower_type': borrower_type
                        })
                    else:
                        error_reason = result.get('reason', 'Unknown error')
                        error_msg = f"Failed to send message to {phone_number} for loan {loan_id}: {error_reason}"
                        
                        # Only add to batch_errors if it's not a rate limit issue (to avoid spam)
                        if 'rate limit' not in error_reason.lower() and len(batch_errors) < 100:
                            batch_errors.append(error_msg)
                        
                        failed_count += 1
                        sub_results.append({
                            'phone_number': raw_phone_number,
                            'loan_id': loan_id,
                            'status': 'failed',
                            'reason': error_reason,
                            'borrower_type': borrower_type
                        })
            
            # Add sub-batch results to overall results
            results.extend(sub_results)
            
            # Calculate and log enhanced metrics for this sub-batch
            sub_batch_time = time.time() - sub_batch_start_time
            sub_batch_success = sum(1 for r in sub_results if r.get('status') == 'success')
            primary_count = sum(1 for r in sub_results if r.get('status') == 'success' and r.get('borrower_type') == 'primary')
            co_borrower_count = sub_batch_success - primary_count
            success_rate = sub_batch_success / len(sub_results) if len(sub_results) > 0 else 0
            
            logger.info(f"Sub-batch {sub_batch_idx + 1} completed: {sub_batch_success}/{len(sub_results)} successful "
                       f"({primary_count} primary, {co_borrower_count} co-borrowers) "
                       f"({success_rate:.1%}) in {sub_batch_time:.2f}s")
            
            # Recalculate optimal batch size for next sub-batch based on current performance
            if sub_batch_idx < num_sub_batches - 1:
                current_overall_success_rate = successful_count / len(results) if len(results) > 0 else 1.0
                optimal_batch_size = calculate_dynamic_batch_size(
                    current_overall_success_rate,
                    total_records,
                    (current_messages_sent + successful_count) % 4000
                )
                
                # Adaptive delay between sub-batches based on performance
                if success_rate < 0.6:  # Poor performance
                    delay_time = 8.0
                    logger.warning(f"Poor sub-batch performance ({success_rate:.1%}). Extended delay: {delay_time}s")
                elif success_rate < 0.8:  # Moderate performance
                    delay_time = 4.0
                elif current_hour_usage > 3200:  # Approaching hourly limit
                    delay_time = 6.0
                    logger.warning(f"Approaching hourly limit ({current_hour_usage}/4000). Throttling: {delay_time}s")
                else:  # Good performance
                    delay_time = 2.0
                
                logger.info(f"Waiting {delay_time}s before next sub-batch (adjusted batch size: {optimal_batch_size})")
                time.sleep(delay_time)

            # Calculate total messages sent (existing + current batch)
            total_messages_sent = current_messages_sent + successful_count
            
            # Update campaign progress more frequently with better error handling
            try:
                update_success = update_campaign_status(
                    pool,
                    campaign_id,
                    template_id, 
                    'processing', 
                    processed_rows=processed_rows_for_update,  # Use sequential processed count
                    whatsapp_messages_sent=total_messages_sent,  # Use cumulative count
                    errors=batch_errors[-50:] if len(batch_errors) > 50 else batch_errors  # Limit error list size
                )
                logger.info(f"Progress update {'successful' if update_success else 'failed'} - " + 
                          f"Processed: {processed_rows_for_update}, Total Sent: {total_messages_sent}, " +
                          f"Previous Sent: {current_messages_sent}, This Batch: {successful_count}")
            except Exception as update_e:
                logger.error(f"Error updating campaign progress: {str(update_e)}")
                # Continue processing even if update fails

        # Batch insert all message entries into database with error handling
        if message_entries:
            try:
                create_message_entries_batch(pool, message_entries)
                logger.info(f"Successfully inserted {len(message_entries)} message entries")
            except Exception as e:
                logger.error(f"Error inserting message entries: {str(e)}")
                batch_errors.append(f"Database insertion error: {str(e)[:100]}")

        # Calculate overall batch processing time and metrics
        processing_time = (datetime.now() - start_time).total_seconds()
        total_messages_sent = current_messages_sent + successful_count
        overall_success_rate = (successful_count / len(results)) * 100 if len(results) > 0 else 0
        
        # Final status update with comprehensive error handling
        try:
            is_last_batch = (batch_number + 1) >= total_batches
            whatsapp_status = 'completed' if is_last_batch else 'processing'
            
            # if is_last_batch:
            #     logger.info(f"Final batch completed. Updating status to {whatsapp_status}")
            
            # Calculate final processed rows
            final_processed_rows = batch_start_processed + total_records
                
            # Enhanced error summary for final update
            if failed_count > 0:
                summary_msg = f"Batch completed with {failed_count} failures out of {len(results)} messages ({(failed_count/len(results))*100:.1f}% failure rate)"
                if len(batch_errors) < 100:  # Only add if we haven't hit the limit
                    batch_errors.append(summary_msg)
            
            # Limit number of errors to avoid database issues
            if len(batch_errors) > 75:
                overflow_count = len(batch_errors) - 74
                batch_errors = batch_errors[:74]
                batch_errors.append(f"Plus {overflow_count} additional errors (truncated to prevent database overflow)")
            
            # Log the total messages count for diagnosis
            # logger.info(f"Final update - Total messages sent: {total_messages_sent} " +
            #           f"(Previous: {current_messages_sent}, This Batch: {successful_count})")
            
            # Make final update with retry logic
            final_update_success = False
            for attempt in range(3):  # Try up to 3 times
                try:
                    final_update_success = update_campaign_status(
                        pool=pool,
                        campaign_id=campaign_id,
                        template_id=template_id,
                        whatsapp_status=whatsapp_status,
                        processed_rows=final_processed_rows,
                        whatsapp_messages_sent=total_messages_sent,
                        errors=batch_errors
                    )
                    if final_update_success:
                        break
                    else:
                        logger.warning(f"Final status update attempt {attempt + 1} failed")
                        if attempt < 2:  # Don't sleep on last attempt
                            time.sleep(2 ** attempt)  # Exponential backoff
                except Exception as e:
                    logger.error(f"Final status update attempt {attempt + 1} exception: {str(e)}")
                    if attempt < 2:
                        time.sleep(2 ** attempt)
            
            # logger.info(f"Final status update {'successful' if final_update_success else 'failed after 3 attempts'}")
            # logger.info(f"Status: {whatsapp_status}, Total Processed: {final_processed_rows}, " + 
            #           f"Total Sent: {total_messages_sent}, Errors: {len(batch_errors)}")
                
        except Exception as e:
            error_msg = f"Error in final status update: {str(e)}"
            logger.error(error_msg)
            batch_errors.append(error_msg)
            
            # Emergency final status update
            if 'is_last_batch' in locals() and is_last_batch:
                try:
                    logger.info("Attempting emergency final status update...")
                    update_campaign_status(
                        pool=pool, 
                        campaign_id=campaign_id,
                        template_id=template_id,
                        whatsapp_status='completed',
                        processed_rows=batch_start_processed + total_records,
                        whatsapp_messages_sent=total_messages_sent,
                        errors=batch_errors[-50:]  # Only last 50 errors
                    )
                except Exception as final_e:
                    logger.error(f"Emergency final status update also failed: {str(final_e)}")

        # Enhanced completion logging with detailed metrics
        primary_sent = sum(1 for r in results if r.get('status') == 'success' and r.get('borrower_type') == 'primary')
        co_borrower_sent = successful_count - primary_sent
        throughput = successful_count / processing_time if processing_time > 0 else 0
        
        logger.info(f"Batch {batch_number} completed: {successful_count} successful ({primary_sent} primary, {co_borrower_sent} co-borrowers), "
                   f"{failed_count} failed, {overall_success_rate:.1f}% success rate, {throughput:.2f} msg/sec, in {processing_time:.2f}s")

        # NEW: Update master_excel_generated status when batch completes
        if campaign_id and pool:
            logger.info(f"Updating master_excel_generated status for campaign {campaign_id}")
            try:
                update_result = update_master_excel_status(pool, campaign_id, status=False)
                if update_result:
                    logger.info(f"Successfully updated master_excel_generated to False for campaign {campaign_id}")
                else:
                    logger.error(f"Failed to update master_excel_generated for campaign {campaign_id}")
            except Exception as e:
                logger.error(f"Exception updating master_excel_generated for campaign {campaign_id}: {str(e)}")

        # Return comprehensive processing data
        return {
            'success': True,
            'campaign_id': campaign_id,
            'batch_number': batch_number,
            'total_processed': final_processed_rows if 'final_processed_rows' in locals() else batch_start_processed + total_records,
            'successful': successful_count,
            'primary_sent': primary_sent,
            'co_borrower_sent': co_borrower_sent,
            'total_messages_sent': total_messages_sent,
            'failed': failed_count,
            'success_rate': overall_success_rate,
            'throughput': throughput,
            'processing_time': processing_time,
            'errors': len(batch_errors),
            'flow_type': flow_type,
            'has_co_borrowers': has_co_borrowers,
            'batch_errors_sample': batch_errors[-10:] if batch_errors else [],  # Last 10 errors for debugging
            'master_excel_updated': True  # Add this flag to indicate the update was attempted
        }

    except Exception as e:
        error_msg = f"Critical error in process_batch: {str(e)}"
        logger.exception(error_msg)
        batch_errors.append(error_msg)
        
        # Enhanced error recovery
        if 'pool' in locals() and 'campaign_id' in locals() and 'template_id' in locals():
            try:
                # Determine current messages sent count if possible
                total_msgs = current_messages_sent if 'current_messages_sent' in locals() else 0
                if 'successful_count' in locals():
                    total_msgs += successful_count
                    
                # Determine processed rows
                processed = current_processed_rows if 'current_processed_rows' in locals() else 0
                
                # Try to update status to failed with retry logic
                for attempt in range(2):
                    try:
                        update_campaign_status(
                            pool, 
                            campaign_id, 
                            template_id, 
                            'failed',
                            processed_rows=processed,
                            whatsapp_messages_sent=total_msgs,
                            errors=batch_errors[-50:]  # Limit errors
                        )
                        logger.info(f"Successfully updated status to failed (attempt {attempt + 1})")
                        break
                    except Exception as update_e:
                        logger.error(f"Failed to update status to failed (attempt {attempt + 1}): {str(update_e)}")
                        if attempt == 0:
                            time.sleep(2)
                            
            except Exception as recovery_e:
                logger.error(f"Error recovery also failed: {str(recovery_e)}")
        
        # Return comprehensive error information
        return {
            'success': False,
            'error': str(e),
            'errors': batch_errors[-20:] if len(batch_errors) > 20 else batch_errors,  # Last 20 errors
            'flow_type': flow_type if 'flow_type' in locals() else 'unknown',
            'campaign_id': campaign_id if 'campaign_id' in locals() else 'unknown',
            'batch_number': batch_number if 'batch_number' in locals() else 'unknown',
            'partial_success_count': successful_count if 'successful_count' in locals() else 0,
            'partial_failure_count': failed_count if 'failed_count' in locals() else 0,
            'master_excel_updated': False  # Indicate the update was not successful
        }
    
    finally:
        # GUARANTEED execution: Update master_excel_generated regardless of success or failure
        if campaign_id and 'pool' in locals() and pool:
            logger.info(f"FINALLY block: Ensuring master_excel_generated is updated for campaign {campaign_id}")
            try:
                update_result = update_master_excel_status(pool, campaign_id, status=False)
                if update_result:
                    logger.info(f"FINALLY: Successfully updated master_excel_generated to False for campaign {campaign_id}")
                else:
                    logger.error(f"FINALLY: Failed to update master_excel_generated for campaign {campaign_id}")
            except Exception as e:
                logger.error(f"FINALLY: Exception updating master_excel_generated for campaign {campaign_id}: {str(e)}")

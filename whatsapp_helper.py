
import logging
import requests
import json
import os
import time
import random
from io import BytesIO
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from collections import deque
from threading import Lock, RLock
import threading
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# WhatsApp API configuration
WHATSAPP_CONFIG_BFSI = {
    "VERSION": os.environ.get("WHATSAPP_VERSION"),
    "PHONE_NUMBER_ID": os.environ.get("WHATSAPP_PHONE_NUMBER_ID"),
    "ACCESS_TOKEN": os.environ.get("WHATSAPP_ACCESS_TOKEN")
}

IS_S21_WHATSAPP_CONFIG = {
    "VERSION": os.environ.get("WHATSAPP_VERSION"),
    "PHONE_NUMBER_ID": os.environ.get("IS_S21_WHATSAPP_PHONE_NUMBER_ID"),
    "ACCESS_TOKEN": os.environ.get("IS_S21_WHATSAPP_ACCESS_TOKEN")
}

# Enhanced rate limiting configuration
MAX_RETRIES = 5  # Increased from 3
BASE_RETRY_DELAY = 3  # Increased from 2
MAX_RETRY_DELAY = 120  # Maximum backoff delay

# More conservative rate limits with dynamic adjustment
CONSERVATIVE_TPS = int(os.environ.get('WHATSAPP_RATE_LIMIT_TPS', 6))  # Very conservative
AGGRESSIVE_TPS = int(os.environ.get('WHATSAPP_AGGRESSIVE_TPS', 12))  # More aggressive when working well
MAX_CALLS_PER_HOUR = int(os.environ.get('WHATSAPP_MAX_HOURLY_CALLS', 3500))  # Buffer for WhatsApp's 4000 limit
SAFE_HOURLY_LIMIT = int(MAX_CALLS_PER_HOUR * 0.9)  # 90% of our conservative limit

# Adaptive batch sizing
MIN_BATCH_SIZE = 3
MAX_BATCH_SIZE = 15
MIN_MESSAGE_BATCH_SIZE = 5
MAX_MESSAGE_BATCH_SIZE = 12

# Threading limits
MAX_CONCURRENT_UPLOADS = 2
MAX_CONCURRENT_MESSAGE_THREADS = 2

# Global rate limiting with enhanced thread safety
rate_limit_data = {
    'calls_per_second': deque(),
    'calls_per_hour': deque(),
    'consecutive_successes': 0,
    'consecutive_failures': 0,
    'current_tps_limit': CONSERVATIVE_TPS,
    'last_rate_limit_time': 0,
    'circuit_breaker_until': 0,
    'hourly_reset_time': time.time() + 3600
}

# Use RLock for reentrant locking
rate_limit_lock = RLock()
circuit_breaker_lock = RLock()

class RateLimitManager:
    """Enhanced rate limit manager with circuit breaker pattern"""
    
    @staticmethod
    def clean_old_entries():
        """Clean old entries from tracking deques"""
        current_time = time.time()
        
        # Clean second-level tracking (last 60 seconds)
        while (rate_limit_data['calls_per_second'] and 
               current_time - rate_limit_data['calls_per_second'][0] > 60):
            rate_limit_data['calls_per_second'].popleft()
        
        # Clean hour-level tracking
        while (rate_limit_data['calls_per_hour'] and 
               current_time - rate_limit_data['calls_per_hour'][0] > 3600):
            rate_limit_data['calls_per_hour'].popleft()
        
        # Reset hourly counter if needed
        if current_time > rate_limit_data['hourly_reset_time']:
            rate_limit_data['calls_per_hour'].clear()
            rate_limit_data['hourly_reset_time'] = current_time + 3600
    
    @staticmethod
    def get_current_metrics():
        """Get current rate limiting metrics"""
        with rate_limit_lock:
            RateLimitManager.clean_old_entries()
            
            current_time = time.time()
            calls_last_minute = len(rate_limit_data['calls_per_second'])
            calls_this_hour = len(rate_limit_data['calls_per_hour'])
            
            # Calculate current TPS over last 60 seconds
            current_tps = calls_last_minute / 60.0
            
            # Check if we're in circuit breaker mode
            circuit_breaker_active = current_time < rate_limit_data['circuit_breaker_until']
            
            return {
                'current_tps': current_tps,
                'calls_this_hour': calls_this_hour,
                'consecutive_successes': rate_limit_data['consecutive_successes'],
                'consecutive_failures': rate_limit_data['consecutive_failures'],
                'current_tps_limit': rate_limit_data['current_tps_limit'],
                'circuit_breaker_active': circuit_breaker_active,
                'seconds_until_circuit_reset': max(0, rate_limit_data['circuit_breaker_until'] - current_time)
            }
    
    @staticmethod
    def record_api_call():
        """Record an API call for rate limiting"""
        with rate_limit_lock:
            current_time = time.time()
            rate_limit_data['calls_per_second'].append(current_time)
            rate_limit_data['calls_per_hour'].append(current_time)
    
    @staticmethod
    def record_success():
        """Record a successful API call"""
        with rate_limit_lock:
            rate_limit_data['consecutive_successes'] += 1
            rate_limit_data['consecutive_failures'] = 0
            
            # Gradually increase TPS limit on sustained success
            if (rate_limit_data['consecutive_successes'] > 0 and 
                rate_limit_data['consecutive_successes'] % 10 == 0 and
                rate_limit_data['current_tps_limit'] < AGGRESSIVE_TPS):
                
                old_limit = rate_limit_data['current_tps_limit']
                rate_limit_data['current_tps_limit'] = min(
                    rate_limit_data['current_tps_limit'] + 1, 
                    AGGRESSIVE_TPS
                )
    
    @staticmethod
    def record_failure(is_rate_limit=False):
        """Record a failed API call"""
        with rate_limit_lock:
            rate_limit_data['consecutive_failures'] += 1
            rate_limit_data['consecutive_successes'] = 0
            
            if is_rate_limit:
                rate_limit_data['last_rate_limit_time'] = time.time()
                
                # Immediately reduce TPS limit on rate limit
                old_limit = rate_limit_data['current_tps_limit']
                rate_limit_data['current_tps_limit'] = max(
                    rate_limit_data['current_tps_limit'] - 2,
                    CONSERVATIVE_TPS // 2
                )
            
            # Activate circuit breaker on consecutive failures
            if rate_limit_data['consecutive_failures'] >= 5:
                with circuit_breaker_lock:
                    rate_limit_data['circuit_breaker_until'] = time.time() + 60  # 1 minute circuit breaker
                    rate_limit_data['current_tps_limit'] = CONSERVATIVE_TPS // 2
    
    @staticmethod
    def wait_for_rate_limit():
        """Enhanced rate limiting with circuit breaker and adaptive delays"""
        with rate_limit_lock:
            current_time = time.time()
            metrics = RateLimitManager.get_current_metrics()
            
            # Circuit breaker check
            if metrics['circuit_breaker_active']:
                wait_time = metrics['seconds_until_circuit_reset'] + random.uniform(1, 3)
                time.sleep(wait_time)
                return
            
            # Critical: Prevent hourly limit breach
            if metrics['calls_this_hour'] >= SAFE_HOURLY_LIMIT:
                # Calculate time until hour resets
                time_until_reset = rate_limit_data['hourly_reset_time'] - current_time
                if time_until_reset > 0:
                    time.sleep(min(time_until_reset + 5, 300))  # Max 5 minute wait
                    return
            
            # Slow down significantly if approaching hourly limit
            if metrics['calls_this_hour'] >= SAFE_HOURLY_LIMIT * 0.85:
                wait_time = random.uniform(8, 12)
                time.sleep(wait_time)
                return
            
            # TPS-based rate limiting
            current_tps_limit = metrics['current_tps_limit']
            
            if metrics['current_tps'] >= current_tps_limit * 0.9:
                # Very close to TPS limit
                wait_time = random.uniform(2, 4)
                time.sleep(wait_time)
            elif metrics['current_tps'] >= current_tps_limit * 0.7:
                # Approaching TPS limit
                wait_time = random.uniform(0.8, 1.5)
                time.sleep(wait_time)
            elif metrics['consecutive_failures'] > 0:
                # Recent failures, be more conservative
                wait_time = random.uniform(0.5, 1.2)
                time.sleep(wait_time)
            else:
                # Normal operation - minimal delay
                time.sleep(random.uniform(0.1, 0.3))

def detect_rate_limit(response):
    """Enhanced rate limit detection"""
    if not response:
        return False
    
    status_code = response.status_code
    response_text = response.text.lower()
    
    # Direct rate limit indicators
    if status_code == 429:
        return True
    
    # WhatsApp-specific rate limit error codes and messages
    rate_limit_indicators = [
        '130429',  # WhatsApp rate limit error code
        'rate limit hit',
        'rate_limit_hit',
        'too many requests',
        'request limit exceeded',
        'api rate limit exceeded',
        'temporarily_unavailable',
        '4000004',  # Another WhatsApp rate limit code
        'rate limiting',
        'throttled',
        'quota exceeded'
    ]
    
    return any(indicator in response_text for indicator in rate_limit_indicators)

def calculate_backoff_delay(retry_count, base_delay=BASE_RETRY_DELAY, max_delay=MAX_RETRY_DELAY):
    """Calculate exponential backoff with jitter"""
    delay = min(base_delay * (2 ** retry_count), max_delay)
    jitter = random.uniform(0.8, 1.2)  # ±20% jitter
    return delay * jitter

def send_message(data, is_s21_notice=None, retry_count=0):
    """Enhanced message sending with robust error handling"""
    
    # Wait for rate limit before attempting
    RateLimitManager.wait_for_rate_limit()
    
    config = IS_S21_WHATSAPP_CONFIG if is_s21_notice else WHATSAPP_CONFIG_BFSI
    
    headers = {
        "Content-type": "application/json",
        "Authorization": f"Bearer {config['ACCESS_TOKEN']}"
    }
    
    url = f"https://graph.facebook.com/{config['VERSION']}/{config['PHONE_NUMBER_ID']}/messages"
    
    try:
        # Record the API call attempt
        RateLimitManager.record_api_call()
        
        # Make request with extended timeout
        response = requests.post(url, headers=headers, data=data, timeout=30)
        print("******** response ********", response.text)
        
        # Enhanced rate limit detection
        is_rate_limited = detect_rate_limit(response)
        
        if is_rate_limited and retry_count < MAX_RETRIES:
            RateLimitManager.record_failure(is_rate_limit=True)
            
            # Get retry-after header or calculate backoff
            retry_after = response.headers.get('Retry-After')
            if retry_after:
                wait_time = int(retry_after) + random.uniform(2, 5)
            else:
                wait_time = calculate_backoff_delay(retry_count, base_delay=5)
            time.sleep(wait_time)
            return send_message(data, is_s21_notice, retry_count + 1)
        
        # Handle server errors with backoff
        elif response.status_code >= 500 and retry_count < MAX_RETRIES:
            RateLimitManager.record_failure()
            wait_time = calculate_backoff_delay(retry_count, base_delay=3)
            time.sleep(wait_time)
            return send_message(data, is_s21_notice, retry_count + 1)
        
        # Handle temporary WhatsApp errors
        elif (response.status_code in [400, 401, 403] and 
              retry_count < MAX_RETRIES and
              any(error in response.text.lower() for error in ['temporarily_unavailable', 'try again later'])):
            RateLimitManager.record_failure()
            wait_time = calculate_backoff_delay(retry_count, base_delay=4)
            time.sleep(wait_time)
            return send_message(data, is_s21_notice, retry_count + 1)
        
        # Success case
        elif response.status_code == 200:
            RateLimitManager.record_success()
            return response.text
        
        # Permanent errors
        else:
            RateLimitManager.record_failure()
            return None
    
    except requests.exceptions.Timeout:
        RateLimitManager.record_failure()
        if retry_count < MAX_RETRIES:
            wait_time = calculate_backoff_delay(retry_count, base_delay=4)
            time.sleep(wait_time)
            return send_message(data, is_s21_notice, retry_count + 1)
        else:
            return None
    
    except requests.exceptions.RequestException as e:
        RateLimitManager.record_failure()
        if retry_count < MAX_RETRIES:
            wait_time = calculate_backoff_delay(retry_count, base_delay=3)
            time.sleep(wait_time)
            return send_message(data, is_s21_notice, retry_count + 1)
        else:
            return None

def send_whatsapp_message(mobile, template_id, is_s21_notice, body_param_text=None, has_body_params=None, media_id=None, file_name=None, lang_code="en", parameters=None):
    """Send a WhatsApp message with optional attachment and body parameters."""

    template = {
        "name": template_id,
        "language": {"code": lang_code}
    }

    components = []

    # Add media/document header only if provided
    if media_id:
        components.append({
            "type": "header",
            "parameters": [
                {
                    "type": "document",
                    "document": {
                        "id": media_id,
                        "filename": file_name or "Document"
                    }
                }
            ]
        })

    # Handle body parameters for generic templates
    if parameters and isinstance(parameters, list):
        # For generic templates with multiple parameters
        body_component = {
            "type": "body",
            "parameters": [
                {
                    "type": "text",
                    "text": str(param)
                } for param in parameters
            ]
        }
        components.append(body_component)
    elif body_param_text and has_body_params is True:
        # For single parameter (backward compatibility)
        components.append({
            "type": "body",
            "parameters": [
                {
                    "type": "text",
                    "text": body_param_text
                }
            ]
        })

    if components:
        template["components"] = components

    data = {
        "messaging_product": "whatsapp",
        "to": mobile,
        "type": "template",
        "template": template
    }

    return send_message(json.dumps(data), is_s21_notice)

def send_conciliation_message(mobile, template_id, parameters, is_s21_notice=True, media_id=None, file_name=None, lang_code="en_US"):
    """Send a conciliation WhatsApp message with specific parameters."""
    template = {
        "name": template_id,
        "language": {"code": lang_code}
    }
    # print("============ sending conciliation message ==============")

    components = []

    # Add media/document header if provided
    if media_id and file_name:
        components.append({
            "type": "header",
            "parameters": [
                {
                    "type": "document",
                    "document": {
                        "id": media_id,
                        "filename": file_name
                    }
                }
            ]
        })

    # Add body parameters
    if parameters and isinstance(parameters, list):
        body_component = {
            "type": "body",
            "parameters": [
                {
                    "type": "text",
                    "text": str(param)
                } for param in parameters
            ]
        }
        components.append(body_component)

    if components:
        template["components"] = components

    data = {
        "messaging_product": "whatsapp",
        "to": mobile,
        "type": "template",
        "template": template
    }
    return send_message(json.dumps(data), is_s21_notice)

def upload_media_to_whatsapp(file_data, file_name, is_s21_notice=None, retry_count=0):
    """Enhanced media upload with robust error handling"""
    
    # Wait for rate limit before attempting
    RateLimitManager.wait_for_rate_limit()
    
    config = IS_S21_WHATSAPP_CONFIG if is_s21_notice else WHATSAPP_CONFIG_BFSI
    url = f"https://graph.facebook.com/{config['VERSION']}/{config['PHONE_NUMBER_ID']}/media"

    headers = {
        "Authorization": f"Bearer {config['ACCESS_TOKEN']}"
    }

    file_stream = BytesIO(file_data)
    files = {
        'file': (file_name, file_stream, 'application/pdf')
    }

    data = {
        'messaging_product': 'whatsapp',
        'type': 'document'
    }

    params = {
        'filename': file_name  
    }

    try:
        # Record API call for rate limiting
        RateLimitManager.record_api_call()
        
        response = requests.post(url, headers=headers, files=files, data=data, params=params, timeout=60)
        
        # Enhanced rate limit detection for media uploads
        is_rate_limited = detect_rate_limit(response)
        
        if is_rate_limited and retry_count < MAX_RETRIES:
            RateLimitManager.record_failure(is_rate_limit=True)
            wait_time = calculate_backoff_delay(retry_count, base_delay=6)
            time.sleep(wait_time)
            return upload_media_to_whatsapp(file_data, file_name, is_s21_notice, retry_count + 1)

        elif response.status_code >= 500 and retry_count < MAX_RETRIES:
            RateLimitManager.record_failure()
            wait_time = calculate_backoff_delay(retry_count, base_delay=4)
            time.sleep(wait_time)
            return upload_media_to_whatsapp(file_data, file_name, is_s21_notice, retry_count + 1)

        elif response.status_code == 200:
            RateLimitManager.record_success()
            return json.loads(response.text)

        else:
            RateLimitManager.record_failure()
            return None

    except requests.exceptions.Timeout:
        RateLimitManager.record_failure()
        if retry_count < MAX_RETRIES:
            wait_time = calculate_backoff_delay(retry_count, base_delay=5)
            time.sleep(wait_time)
            return upload_media_to_whatsapp(file_data, file_name, is_s21_notice, retry_count + 1)
        else:
            return None
            
    except requests.exceptions.RequestException as e:
        RateLimitManager.record_failure()
        if retry_count < MAX_RETRIES:
            wait_time = calculate_backoff_delay(retry_count, base_delay=4)
            logger.warning(f"Media upload retry {retry_count + 1}/{MAX_RETRIES}: {str(e)[:100]}")
            time.sleep(wait_time)
            return upload_media_to_whatsapp(file_data, file_name, is_s21_notice, retry_count + 1)
        else:
            return None

def get_adaptive_batch_size(success_rate, total_messages):
    """Calculate adaptive batch size based on success rate and volume"""
    if total_messages <= 100:
        # Small batches - use smaller sizes
        base_size = MIN_BATCH_SIZE if success_rate < 0.8 else MIN_BATCH_SIZE + 2
    elif total_messages <= 1000:
        # Medium batches
        base_size = MIN_BATCH_SIZE + 2 if success_rate < 0.8 else MIN_BATCH_SIZE + 4
    else:
        # Large batches - more conservative
        base_size = MIN_BATCH_SIZE + 1 if success_rate < 0.8 else MAX_BATCH_SIZE // 2
    
    return min(max(base_size, MIN_BATCH_SIZE), MAX_BATCH_SIZE)

def batch_upload_media(file_data_map, is_s21_notice, batch_size=None):
    """Enhanced batch media upload with adaptive sizing and robust error handling"""
    media_ids = {}
    total_files = len(file_data_map)
    
    if not file_data_map:
        return media_ids
    
    # Use adaptive batch sizing if not specified
    if batch_size is None:
        batch_size = get_adaptive_batch_size(1.0, total_files)  # Start optimistic
    
    # Split into batches
    file_items = list(file_data_map.items())
    batches = [file_items[i:i+batch_size] for i in range(0, total_files, batch_size)]
    
    overall_success_count = 0
    
    for batch_idx, batch in enumerate(batches):
        batch_start_time = time.time()
        
        def upload_single_file(file_tuple):
            """Upload single file with comprehensive error handling"""
            file_name, file_data = file_tuple
            
            # Add small delay to spread uploads
            time.sleep(random.uniform(0.2, 0.8))
            
            try:
                response = upload_media_to_whatsapp(file_data, file_name, is_s21_notice)
                if response and 'id' in response:
                    return file_name, response['id']
                else:
                    return file_name, None
                        
            except Exception as e:
                return file_name, None

        # Process batch with limited concurrency
        batch_media_ids = {}
        max_workers = min(MAX_CONCURRENT_UPLOADS, len(batch))
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(upload_single_file, file_tuple): file_tuple[0] for file_tuple in batch}

            for future in as_completed(futures):
                try:
                    file_name, media_id = future.result(timeout=120)  # 2 minutes timeout per upload
                    if media_id:
                        batch_media_ids[file_name] = media_id
                        overall_success_count += 1
                    else:
                        logger.warning(f"Failed to upload media: {file_name}")
                except Exception as e:
                    file_name = futures[future]
                    logger.error(f"Media upload exception for {file_name}: {str(e)[:100]}")

        media_ids.update(batch_media_ids)
        
        # Calculate batch metrics and adjust strategy
        batch_time = time.time() - batch_start_time
        batch_success_rate = len(batch_media_ids) / len(batch) if len(batch) > 0 else 0
        overall_success_rate = overall_success_count / ((batch_idx + 1) * len(batch)) if batch_idx * len(batch) + len(batch) > 0 else 0
        
        # Adaptive delay between media batches
        if batch_idx < len(batches) - 1:
            if batch_success_rate < 0.7:
                delay_time = random.uniform(8, 12)  # Long delay for poor performance
            elif batch_success_rate < 0.9:
                delay_time = random.uniform(4, 6)  # Medium delay
            else:
                delay_time = random.uniform(2, 3)  # Short delay for good performance
            
            time.sleep(delay_time)
    
    overall_success_rate = overall_success_count / total_files if total_files > 0 else 0
    
    return media_ids

def send_messages_batch(messages_data, is_s21_notice, has_body_params=None, delay=None, batch_size=None):
    """Enhanced message batch sending with adaptive rate limiting and robust error handling"""
    
    if not messages_data:
        return []
    
    results = []
    total_messages = len(messages_data)
    # print("--------total_messages--------", total_messages)
    
    # Use adaptive batch sizing
    if batch_size is None:
        batch_size = get_adaptive_batch_size(1.0, total_messages)  # Start optimistic
    
    # Split into adaptive sub-batches
    sub_batches = [messages_data[i:i+batch_size] for i in range(0, total_messages, batch_size)]
    
    overall_success_count = 0
    
    for batch_idx, sub_batch in enumerate(sub_batches):
        batch_start_time = time.time()
        
        def send_single_message(msg_data):
            """Send single message with comprehensive error handling"""
            try:
                # Add randomized delay to spread load
                time.sleep(random.uniform(0.2, 0.6))
                
                flow_type = msg_data.get('flow_type', 'arbitration')

                if flow_type == 'conciliation':
                    # Use conciliation-specific function
                    response = send_conciliation_message(
                        mobile=msg_data.get('mobile'),
                        template_id=msg_data.get('template_id'),
                        parameters=msg_data.get('parameters', []),
                        is_s21_notice=is_s21_notice,
                        media_id=msg_data.get('media_id'),
                        file_name=msg_data.get('file_name'),
                        lang_code=msg_data.get('lang_code', 'en_US')
                    )
                else:
                    # Use original arbitration function
                    response = send_whatsapp_message(
                        mobile=msg_data.get('mobile'),
                        template_id=msg_data.get('template_id'),
                        is_s21_notice=is_s21_notice,
                        body_param_text=msg_data.get('body_param_text'),
                        parameters=msg_data.get('parameters', []),
                        media_id=msg_data.get('media_id'),
                        file_name=msg_data.get('file_name'),
                        lang_code=msg_data.get('lang_code', 'en'),
                        has_body_params=has_body_params,
                    )
                
                if response:
                    try:
                        msg_resp = json.loads(response)
                        if "contacts" in msg_resp and "messages" in msg_resp:
                            msg_id = msg_resp["messages"][0]["id"]
                            return {
                                'status': 'success',
                                'msg_id': msg_id,
                                'original_data': msg_data
                            }
                        else:
                            return {
                                'status': 'failed',
                                'reason': 'Invalid API response structure',
                                'original_data': msg_data
                            }
                    except (json.JSONDecodeError, KeyError, IndexError) as e:
                        return {
                            'status': 'failed',
                            'reason': f'Response parsing error: {str(e)[:100]}',
                            'original_data': msg_data
                        }
                else:
                    return {
                        'status': 'failed',
                        'reason': 'No response from WhatsApp API',
                        'original_data': msg_data
                    }
            except Exception as e:
                return {
                    'status': 'failed',
                    'reason': f'Send exception: {str(e)[:100]}',
                    'original_data': msg_data
                }

        # Process batch with limited concurrency
        batch_results = []
        max_workers = min(MAX_CONCURRENT_MESSAGE_THREADS, len(sub_batch))
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(send_single_message, msg_data): msg_data for msg_data in sub_batch}
            
            for future in as_completed(futures):
                try:
                    result = future.result(timeout=45)  # 45s timeout per message
                    batch_results.append(result)
                    
                    # Track success for adaptive batching
                    if result.get('status') == 'success':
                        overall_success_count += 1
                        
                except Exception as e:
                    msg_data = futures[future]
                    batch_results.append({
                        'status': 'failed',
                        'reason': f'Future timeout/exception: {str(e)[:100]}',
                        'original_data': msg_data
                    })

        results.extend(batch_results)
        
        # Calculate batch performance metrics
        batch_success_count = sum(1 for r in batch_results if r.get('status') == 'success')
        batch_time = time.time() - batch_start_time
        batch_success_rate = (batch_success_count / len(sub_batch)) if len(sub_batch) > 0 else 0
        overall_success_rate = (overall_success_count / ((batch_idx + 1) * len(sub_batch))) if (batch_idx + 1) * len(sub_batch) > 0 else 0
        throughput = batch_success_count / batch_time if batch_time > 0 else 0
        
        # Count different borrower types
        primary_count = sum(1 for r in batch_results if r.get('status') == 'success' and 
                           r.get('original_data', {}).get('borrower_type') == 'primary')
        co_borrower_count = batch_success_count - primary_count
        
        if co_borrower_count > 0:
            logger.info(f"Message batch {batch_idx+1} completed: {batch_success_count}/{len(sub_batch)} successful "
                       f"({primary_count} primary, {co_borrower_count} co-borrowers) "
                       f"({batch_success_rate:.1%}) in {batch_time:.2f}s ({throughput:.2f} msg/s)")
        else:
            logger.info(f"Message batch {batch_idx+1} completed: {batch_success_count}/{len(sub_batch)} successful "
                       f"({batch_success_rate:.1%}) in {batch_time:.2f}s ({throughput:.2f} msg/s)")
        
        # Adaptive strategy for next batch
        if batch_idx < len(sub_batches) - 1:
            # Get current rate limiting metrics
            metrics = RateLimitManager.get_current_metrics()
            
            # Calculate dynamic delay based on performance
            if batch_success_rate < 0.6:
                # Poor performance - long delay and reduce batch size
                dynamic_delay = random.uniform(15, 25)
            elif batch_success_rate < 0.8:
                # Moderate performance - medium delay
                dynamic_delay = random.uniform(8, 12)
            elif metrics['calls_this_hour'] > SAFE_HOURLY_LIMIT * 0.8:
                # Approaching hourly limit - slow down significantly
                dynamic_delay = random.uniform(10, 15)
            elif metrics['current_tps'] > metrics['current_tps_limit'] * 0.8:
                # Approaching TPS limit - moderate delay
                dynamic_delay = random.uniform(5, 8)
            else:
                # Good performance - shorter delay
                dynamic_delay = random.uniform(3, 5)
            time.sleep(dynamic_delay)

    # Final summary
    total_success = sum(1 for r in results if r.get('status') == 'success')
    overall_success_rate = (total_success / total_messages) if total_messages > 0 else 0
    
    # Count overall borrower types
    overall_primary = sum(1 for r in results if r.get('status') == 'success' and 
                         r.get('original_data', {}).get('borrower_type') == 'primary')
    overall_co_borrower = total_success - overall_primary
    
    if overall_co_borrower > 0:
        logger.info(f"Message batch processing complete: {total_success}/{total_messages} successful "
                   f"({overall_primary} primary, {overall_co_borrower} co-borrowers) ({overall_success_rate:.1%})")
    else:
        logger.info(f"Message batch processing complete: {total_success}/{total_messages} successful "
                   f"({overall_success_rate:.1%})")
    
    # Log final rate limiting metrics
    final_metrics = RateLimitManager.get_current_metrics()
    logger.info(f"Final metrics - TPS: {final_metrics['current_tps']:.2f}, "
               f"Hourly calls: {final_metrics['calls_this_hour']}, "
               f"Success streak: {final_metrics['consecutive_successes']}")
    
    return results

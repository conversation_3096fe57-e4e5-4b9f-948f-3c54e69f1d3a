
import logging
import pandas as pd
from datetime import datetime, time

# Configure logging
logger = logging.getLogger(__name__)

# def format_phone_number(phone_number):
#     """
#     Format phone number to ensure it's in the correct format for WhatsApp.
#     Returns None if the phone number is invalid.
#     """
#     try:
#         # Remove any non-digit characters
#         phone_number = str(int(float(phone_number)))
#         phone_number = ''.join(filter(str.isdigit, phone_number))
#         print("removed empty space from phone number: ", phone_number)

#         # Handle different formats
#         if phone_number.startswith('+'):
#             phone_number = phone_number[1:]
#             print("removed + from phone number: ", phone_number)

#         # Remove country code if it's there (assuming 91 for India)
#         if phone_number.startswith('91') and len(phone_number) == 12:
#             phone_number = phone_number[2:]
#             print("removed 91 from phone number: ", phone_number)

#         # Validate phone number (assuming 10 digits for India)
#         if not phone_number.isdigit() or len(phone_number) != 10:
#             return None

#         # Add country code
#         return '91' + phone_number

#     except Exception as e:
#         logger.error(f"Error formatting phone number {phone_number}: {str(e)}")
#         return None

def format_phone_number(phone_number):
    try:
        # Convert to string first to handle any input type
        phone_number_str = str(phone_number)

        # Handle scientific notation (e.g., 91.12e9, 9.112e9)
        if 'e' in phone_number_str.lower() or '.' in phone_number_str:
            try:
                # Convert scientific notation to integer
                phone_number = str(int(float(phone_number)))
            except:
                phone_number = phone_number_str
        else:
            phone_number = phone_number_str

        # Remove any non-digit characters (spaces, hyphens, plus signs, etc.)
        phone_number = ''.join(filter(str.isdigit, phone_number))

        # Remove country code if it's there (assuming 91 for India)
        if phone_number.startswith('91') and len(phone_number) == 12:
            phone_number = phone_number[2:]

        # Validate phone number (assuming 10 digits for India)
        if not phone_number.isdigit() or len(phone_number) != 10: 
            return None

        # Add country code
        formatted_number = '91' + phone_number
        return formatted_number

    except Exception as e:
        print(f"Error formatting phone number {phone_number}: {str(e)}")
        return None


def prepare_conciliation_parameters(row_data, client_name, is_termination_notice, is_s138_notice, is_payment_request_notice, sarfaeri_act_notice,
                                  conciliation_notice_1, conciliation_notice_2, conciliation_notice_3, 
                                  conciliation_notice_4,  template_parameter_mapping, sheet2_data=None):
    """
    Prepare parameters for conciliation templates based on template type and row data.
    Args:
        row_data: pandas Series containing row data from Excel
        client_name: Name of the client
        is_termination_notice: Boolean flag for termination notice
        is_payment_request_notice: Boolean flag for payment request notice
        conciliation_notice_1: Boolean flag for conciliation notice 1
        conciliation_notice_2: Boolean flag for conciliation notice 2
        conciliation_notice_3: Boolean flag for conciliation notice 3
        conciliation_notice_4: Boolean flag for conciliation notice 4
        template_parameter_mapping: Dictionary mapping parameter positions to column names
        sheet2_data: Dictionary containing Sheet 2 data (NEW)
    Returns:
        List of parameter values for the template
    """
    parameters = []

    try:
        if is_termination_notice:
            # PFL Termination Notice parameters
            # Expected order: borrower_name, loan_number, notice_date, notice_amount, payment_link
            parameters = [
                str(client_name),
                str(row_data.get('Loan ID', '')),
                parse_excel_date(row_data.get('Notice Date', '')),
                format_currency(row_data.get('Notice Amount', 0)),
                str(row_data.get('Payment Link', '')),
                str(row_data.get('Name of the Borrower', '')),
            ]

        elif is_s138_notice:
            # Demand Notice parameters
            # Expected order: borrower_name, loan_number, outstanding_amount, due_date
            parameters = [
                str(row_data.get('Name of the Borrower', '')),
                str(client_name),
                str(row_data.get('Loan ID', '')),
                parse_excel_date(row_data.get('Dishonour Date', '')),
                parse_excel_date(row_data.get('Notice Date', '')),
                format_currency(row_data.get('Notice Amount', 0)),
                str(row_data.get('Payment Link', '')),
            ]

        elif is_payment_request_notice:
            # Demand Notice parameters
            # Expected order: borrower_name, loan_number, outstanding_amount, due_date
            parameters = [
                str(row_data.get('Name of the Borrower', '')),
                str(client_name),
                str(row_data.get('Loan ID', '')),
                parse_excel_date(row_data.get('Notice Date', '')),
                format_currency(row_data.get('Notice Amount', 0)),
                format_currency(row_data.get('Notice Amount', 0)),
                str(row_data.get('Payment Link', '')),
            ]
        elif sarfaeri_act_notice:
            parameters = [
                str(row_data.get('Name of the Borrower', '')),
                str(client_name),
                str(row_data.get('Payment Link', '')),
            ]
        elif conciliation_notice_1:
            # Conciliation Notice 1 parameters
            # Use Sheet 2 data if available, otherwise fall back to row data
            if sheet2_data:
                parameters = [
                    str(row_data.get('Loan ID', '')),
                    str(client_name),
                    parse_excel_date(sheet2_data.get('date', '')),
                    format_time(sheet2_data.get('start_time', '')),
                    format_time(sheet2_data.get('end_time', '')),
                    str(sheet2_data.get('link', '')),
                ]
            else:
                # Fallback to row data
                parameters = [
                    str(row_data.get('Loan ID', '')),
                    str(client_name),
                    parse_excel_date(row_data.get('date', '')),
                    format_time(row_data.get('start_time', '')),
                    format_time(row_data.get('end_time', '')),
                    str(row_data.get('link', '')),
                ]

        elif conciliation_notice_2:
            # Conciliation Notice 2 parameters
            # Use Sheet 2 data if available, otherwise fall back to row data
            if sheet2_data:
                parameters = [
                    str(client_name),
                    parse_excel_date(sheet2_data.get('date', '')),
                    format_time(sheet2_data.get('start_time', '')),
                    format_time(sheet2_data.get('end_time', '')),
                    str(sheet2_data.get('link', '')),
                ]
            else:
                # Fallback to row data
                parameters = [
                    str(client_name),
                    parse_excel_date(row_data.get('date', '')),
                    format_time(row_data.get('start_time', '')),
                    format_time(row_data.get('end_time', '')),
                    str(row_data.get('link', '')),
                ]
            
        elif conciliation_notice_3:
            # Conciliation Notice 3 parameters
            # Use Sheet 2 data if available, otherwise fall back to row data
            if sheet2_data:
                parameters = [
                    parse_excel_date(sheet2_data.get('date', '')),
                    str(client_name),
                    parse_excel_date(sheet2_data.get('date', '')),
                    format_time(sheet2_data.get('start_time', '')),
                    format_time(sheet2_data.get('end_time', '')),
                    str(sheet2_data.get('link', '')),
                ]
            else:
                # Fallback to row data
                parameters = [
                    parse_excel_date(row_data.get('date', '')),
                    str(client_name),
                    parse_excel_date(row_data.get('date', '')),
                    format_time(row_data.get('start_time', '')),
                    format_time(row_data.get('end_time', '')),
                    str(row_data.get('link', '')),
                ]

        elif conciliation_notice_4:
            # Conciliation Notice 4 parameters
            # Use Sheet 2 data if available, otherwise fall back to row data
            if sheet2_data:
                parameters = [
                    str(client_name),
                    str(sheet2_data.get('link', '')),
                ]
            else:
                # Fallback to row data
                parameters = [
                    str(client_name),
                    str(row_data.get('link', '')),
                ]
            
        elif template_parameter_mapping:
            # Generic template with custom parameter mapping
            # Sort by parameter position to ensure correct order
            sorted_mapping = sorted(template_parameter_mapping.items())
            
            for position, column_name in sorted_mapping:
                # First try to get value from Sheet 2 data, then from row data
                value = ''
                if sheet2_data and column_name in sheet2_data:
                    value = sheet2_data.get(column_name, '')
                elif column_name in row_data:
                    value = row_data.get(column_name, '')
                
                # Handle different data types
                if pd.isna(value):
                    value = ''
                elif isinstance(value, (int, float)):
                    # Format numbers appropriately
                    if column_name.lower() in ['amount', 'notice_amount', 'payment', 'outstanding_amount', 
                                             'final_amount', 'settlement_amount', 'reminder_amount']:
                        value = format_currency(value)
                    else:
                        value = str(int(value)) if value == int(value) else str(value)
                else:
                    value = str(value)
                
                parameters.append(value)
        else:
            # Default fallback - just use loan number
            parameters = [str(row_data.get('Loan ID', ''))]
            
        # Clean parameters - remove None values and ensure all are strings
        parameters = [str(param) if param is not None else '' for param in parameters]
        return parameters
        
    except Exception as e:
        logger.error(f"Error preparing conciliation parameters: {str(e)}")
        # Return basic parameter as fallback
        return [str(row_data.get('Loan ID', ''))]


def format_currency(amount):
    """
    Format amount as currency with Indian Rupee symbol.
    Args:
        amount: Numeric amount
    Returns:
        Formatted currency string
    """
    try:
        if pd.isna(amount) or amount == '':
            return '₹0.00'

        amount = float(amount)
        return f"₹{amount:,.2f}"
    except (ValueError, TypeError):
        return '₹0.00'


def format_time(time_value):
    try:
        if pd.isna(time_value) or time_value == '':
            return ''

        # Convert to string first
        time_str = str(time_value)

        # Common time formats to try
        time_formats = [
            '%H:%M:%S',     # 10:00:00
            '%H:%M',        # 10:00
            '%I:%M %p',     # 10:00 AM
            '%I:%M:%S %p',  # 10:00:00 AM
        ]

        for fmt in time_formats:
            try:
                if fmt in ['%H:%M:%S', '%H:%M']:
                    # 24-hour format, need to convert to 12-hour
                    parsed_time = datetime.strptime(time_str, fmt).time()
                    # Convert to 12-hour format
                    dt = datetime.combine(datetime.today(), parsed_time)
                    return dt.strftime('%I:%M %p')
                else:
                    # Already in 12-hour format
                    parsed_time = datetime.strptime(time_str, fmt)
                    return parsed_time.strftime('%I:%M %p')
            except ValueError:
                continue
        # If it's just a number (like 18 for 6 PM)
        try:
            hour = int(float(time_str))
            if 0 <= hour <= 23:
                dt = datetime.strptime(f"{hour:02d}:00", "%H:%M")
                return dt.strftime('%I:%M %p')
        except:
            pass
        return str(time_value)

    except Exception as e:
        return str(time_value)


def parse_excel_date(value):
    try:
        # Handle timestamp in milliseconds
        if isinstance(value, (int, float)) and value > 1e12:
            return pd.to_datetime(value, unit='ms').strftime('%d-%m-%Y')
        # Handle Excel serial date format
        elif isinstance(value, (int, float)):
            return pd.to_datetime('1899-12-30') + pd.to_timedelta(value, unit='D')
        # Handle string date
        elif isinstance(value, str):
            return pd.to_datetime(value).strftime('%d-%m-%Y')
        # Handle already-converted datetime
        elif isinstance(value, pd.Timestamp):
            return value.strftime('%d-%m-%Y')
    except Exception:
        pass
    return str(value)

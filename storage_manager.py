

import logging
from google.cloud import storage
from google.oauth2 import service_account
import os
import json

# Configure logging
logger = logging.getLogger(__name__)

# GCS configuration
GCS_BUCKET_NAME = os.environ.get("GCS_BUCKET_NAME", "your_bucket_name")
CREDENTIALS_DICT = json.loads(os.environ.get("GOOGLE_APPLICATION_CREDENTIALS_JSON", "{}"))

def get_storage_client():
    """Get a Google Cloud Storage client."""
    if CREDENTIALS_DICT:
        credentials = service_account.Credentials.from_service_account_info(CREDENTIALS_DICT)
        return storage.Client(credentials=credentials)
    return storage.Client()

def read_file_from_gcs(bucket_name, file_path):
    """Read a file from Google Cloud Storage."""
    try:
        storage_client = get_storage_client()
        bucket = storage_client.bucket(bucket_name or GCS_BUCKET_NAME)
        blob = bucket.blob(file_path)
        return blob.download_as_bytes()
    except Exception as e:
        logger.error(f"Error reading file from GCS: {str(e)}")
        raise

def list_files_in_gcs(bucket_name, prefix=None):
    """List files in a GCS bucket with an optional prefix."""
    try:
        storage_client = get_storage_client()
        bucket = storage_client.bucket(bucket_name or GCS_BUCKET_NAME)
        blobs = bucket.list_blobs(prefix=prefix)
        return [blob.name for blob in blobs]
    except Exception as e:
        logger.error(f"Error listing files in GCS: {str(e)}")
        raise
